@model Odrc.Dots.Areas.Transfers.Models.Mosci.MosciPageViewModel

@{
    ViewBag.ScreenDescription = "Schedule Inmate Move  -  IMOSC";
}


    @if (!string.IsNullOrWhiteSpace(ViewBag.Message))
    {
        <div class="alert alert-success fade in">
            <a href="#" class="close" data-dismiss="alert" aria-label="close">&times;</a>
            @ViewBag.Message
        </div>
    }
    @if (!string.IsNullOrWhiteSpace(ViewBag.ErrorMessage))
    {
        <div class="alert alert-danger fade in">
            <a href="#" class="close" data-dismiss="alert" aria-label="close">&times;</a>
            @ViewBag.ErrorMessage
        </div>
    }

    @if (!string.IsNullOrWhiteSpace(Model.Message))
    {
        <div class="alert alert-success fade in">
            <a href="#" class="close" data-dismiss="alert" aria-label="close">&times;</a>
            @Model.Message
        </div>
    }
    @if (!string.IsNullOrWhiteSpace(Model.ErrorMessage))
    {
        <div class="alert alert-danger fade in">
            <a href="#" class="close" data-dismiss="alert" aria-label="close">&times;</a>
            @Model.ErrorMessage
        </div>
    }


    <div id="divErrorMessage" class="alert alert-danger fade in" style="display:none">
        <a href="#" class="close" data-dismiss="alert" aria-label="close">X</a>
        <span id="ErrorMessage"></span>
    </div>


    @*<div id="divMessage" class="alert alert-success fade in" style="display:none">
            <a href="#" class="close" data-dismiss="alert" aria-label="close">X</a>
            <span id="Message"></span>
        </div>*@

    @* Hidden fields for search functionality *@
    @Html.HiddenFor(m => m.SearchPrefix)
    @Html.HiddenFor(m => m.SearchOffenderId)



    @using (Html.BeginForm("Mosci", "Mosci", new { area = "Transfers" }, FormMethod.Post, new { @id = "Mosci", @class = "form-horizontal" }))
    {
        @Html.AntiForgeryToken()

        @* Hidden field for JSON model data (used by JavaScript) *@
        @Html.Hidden("modelJson", "", new { id = "modelJson" })

        @* Hidden fields for auto-population functionality *@
        @Html.Hidden("autoPopulateRowIndex", "", new { id = "autoPopulateRowIndex" })

        <div id="Housing-Manage" class="no-print">
            <div class="row">
                <div class="col-md-12">
                    <div class="divFindOffender">
                        <div class="panel panel-primary">
                            <div class="panel-heading">
                                Find Inmate
                            </div>
                            <div class="panel-body">
                                <div class="form-inline">
                                    <div class="form-group col-xs-12 col-sm-6 col-md-6">
                                        @Html.DropDownListFor(m => m.SearchPrefix, Model.PrefixOptions, new { @class = "form-control input-sm", @id = "searchPrefixDropdown" })
                                        @Html.TextBoxFor(m => m.SearchOffenderId, new { @class = "form-control input-sm onlyNumeric", @autofocus = "autofocus", @id = "txtInmateNum", maxlength = "6" })
                                        @* <button id="btnFindOffender" type="button" class="btn btn-primary" name="submitAction" value="Search">
                                                <span>Find Inmate</span>
                                            </button> *@
                                        <button type="submit" class="btn btn-primary" name="submitAction" value="Search">
                                            <span>Find Inmate</span>
                                        </button>
                                        @*<button type="submit" name="submitAction" value="Search" class="btn btn-info" title="Server-side search">
                                                <span class="glyphicon glyphicon-search"></span> Server Search
                                            </button>*@
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        //-------------------------------------------------
        <div class="panel panel-primary">
            <div class="panel-heading">
                Schedule Inmate Move&nbsp;&nbsp;-&nbsp;&nbsp;MOSCI
            </div>
            <div class="panel-body">
                <div class="table-responsive">
                    <table id="inmateTable" class="table table-bordered table-condensed">
                        <thead class="odrc-header-row">
                            <tr>
                                <td style="width:70px;">Prefix</td>
                                <td style="width:120px;">Offender #</td>
                                <td style="width:150px;">Last Name</td>
                                <td style="width:150px;">First Name</td>
                                <td style="width:120px;">From</td>
                                <td style="width:140px;">To</td>
                                <td style="width:140px;">Scheduled Date</td>
                                <td style="width:120px;">Comments</td>
                                <td style="width:50px;">Remove</td>
                                <td style="width:50px;">Delete</td>
                            </tr>
                        </thead>
                        <tbody>
                            @for (int i = 0; i < Model.Inmates.Count; i++)
                            {
                                <tr @if (i == 0) { <text> id="inmate-row-template" </text> }>
                                    <td>
                                        @Html.DropDownListFor(m => m.Inmates[i].InmateIdPrefix, Model.PrefixOptions, new { @class = "form-control input-sm" })
                                        @Html.HiddenFor(m => m.Inmates[i].Recno)
                                        @Html.HiddenFor(m => m.Inmates[i].OffenderId)
                                    </td>
                                    <td>@Html.TextBoxFor(m => m.Inmates[i].OffenderId, new { @class = "form-control input-sm onlyNumeric auto-populate-field", @maxlength = "6" })</td>
                                    <td>@Html.TextBoxFor(m => m.Inmates[i].LastName, new { @class = "form-control input-sm", @readonly = "readonly" })</td>
                                    <td>@Html.TextBoxFor(m => m.Inmates[i].FirstName, new { @class = "form-control input-sm", @readonly = "readonly" })</td>
                                    <td>
                                        @*<select name="FromInstitution" class="form-control input-sm">
                                                <option>MANCINI</option>

                                            </select>*@
                                        @{

                                            var frominsText = "";
                                            if (Model.Inmates[i].Instno.HasValue)
                                            {
                                                var fromOption = Model.InstnoDrp.FirstOrDefault(x => x.Value == Model.Inmates[i].Instno.ToString());
                                                frominsText = fromOption?.Text ?? "";
                                            }
                                            else if (Model.Inmates[i].Instname != null)
                                            {
                                                frominsText = Model.Inmates[i].Instname;
                                            }
                                        }

                                        @Html.TextBoxFor(m => m.Inmates[i].Instno, new { @class = "form-control input-sm", @readonly = "readonly", @style = "display:none;" })
                                        @Html.TextBox("frominsText", frominsText, new { @class = "form-control input-sm", @readonly = "readonly" })
                                    </td>
                                    @*<td>@Html.TextBoxFor(m => m.Inmates[i].SchdInst, new { @class = "form-control input-sm" })</td>*@
                                    <td>
                                        @{
                                            var item = Model.SchdInstDrp
                                                .Select(x => new SelectListItem
                                                {
                                                    Text = x.Text,
                                                    Value = x.Value,
                                                    Selected = (x.Value == Model.Inmates[i].SchdInst.ToString())

                                                });
                                        }
                                        @Html.DropDownListFor(m => m.Inmates[i].SchdInst, item, new { @class = "form-control input-sm" })

                                        @*@Html.DropDownListFor(m => m.Inmates[i].SchdInst, Model.SchdInstDrp,"Select", new { @class = "form-control input-sm" })*@

                                    </td>
                                    @*<td>
                                            @Html.TextBoxFor(m => m.Inmates[i].SchDate, "{0:MM/dd/yyyy}", new { @class = "form-control input-sm" })
                                            <span class="glyphicon glyphicon-calendar"></span>
                                        </td>*@
                                    @*<td>
                                            <div class="input-group scheduled-date">
                                                @Html.TextBoxFor(m => m.Inmates[i].SchDate, "{0:MM/dd/yyyy}", new { @class = "form-control input-sm", @autocomplete = "off", @placeholder = "MM/DD/YYYY", @readonly = "readonly" })
                                                <span class="input-group-addon date-icon">
                                                    <span class="glyphicon glyphicon-calendar"></span>
                                                </span>
                                            </div>
                                        </td>*@
                                    <td>
                                        <div class="input-group input-group-sm">
                                            @Html.TextBoxFor(m => m.Inmates[i].SchDate,
                                           new
                                                {
                                               @class = "form-control input-sm datepicker-input",
                                               @Value = (Model.Inmates[i].SchDate == DateTime.MinValue ? "" : Model.Inmates[i].SchDate.ToString("MM/dd/yyyy")),
                                               PlaceHolder = "MM/dd/yyyy",
                                               autocomplete = "off"
                                           })
                                            <span class="input-group-addon datepicker-trigger" style="cursor: pointer;">
                                                <span class="glyphicon glyphicon-calendar"></span>
                                            </span>
                                        </div>
                                    </td>
                                    <td>@Html.TextAreaFor(m => m.Inmates[i].Descrl, new { @class = "form-control input-sm comment-textarea", @rows = "2", @style = "resize: vertical; min-height: 34px;" })</td>
                                    <td class="text-center">@Html.CheckBoxFor(m => m.Inmates[i].IsMarkedForRemoval)</td>
                                    <td class="text-center">@Html.CheckBoxFor(m => m.Inmates[i].IsMarkedForDeletion)</td>
                                </tr>
                            }
                        </tbody>
                    </table>

                    <!-- Hidden template row for when no rows exist -->
                    <table style="display: none;">
                        <tbody>
                            <tr id="empty-row-template">
                                <td>
                                    @Html.DropDownList("TemplateInmateIdPrefix", Model.PrefixOptions, new { @class = "form-control input-sm", @id = "TemplateInmateIdPrefix" })
                                    <input name="TemplateRecno" id="TemplateRecno" type="hidden" value="0" />
                                    <input name="TemplateOffenderId" id="TemplateOffenderId_Hidden" type="hidden" value="" />
                                </td>
                                <td><input name="TemplateOffenderId" id="TemplateOffenderId" class="form-control input-sm onlyNumeric auto-populate-field" maxlength="6" type="text" value="" data-row-index="0" /></td>
                                <td><input name="TemplateLastName" id="TemplateLastName" class="form-control input-sm" readonly="readonly" type="text" value="" /></td>
                                <td><input name="TemplateFirstName" id="TemplateFirstName" class="form-control input-sm" readonly="readonly" type="text" value="" /></td>
                                <td>
                                    <input name="TemplateInstno" id="TemplateInstno" class="form-control input-sm" readonly="readonly" style="display:none;" type="text" value="" />
                                    <input name="TemplatefrominsText" id="TemplatefrominsText" class="form-control input-sm" readonly="readonly" type="text" value="" />
                                </td>
                                <td>
                                    @Html.DropDownList("TemplateSchdInst", Model.SchdInstDrp, new { @class = "form-control input-sm", @id = "TemplateSchdInst" })
                                </td>
                                <td>
                                    <div class="input-group input-group-sm">
                                        <input name="TemplateSchDate" id="TemplateSchDate" class="form-control input-sm datepicker-input" placeholder="MM/dd/yyyy" autocomplete="off" type="text" value="" />
                                        <span class="input-group-addon datepicker-trigger" style="cursor: pointer;">
                                            <span class="glyphicon glyphicon-calendar"></span>
                                        </span>
                                    </div>
                                </td>
                                <td><textarea name="TemplateDescrl" id="TemplateDescrl" class="form-control input-sm comment-textarea" rows="2" style="resize: vertical; min-height: 34px;"></textarea></td>
                                <td class="text-center">
                                    <input name="TemplateIsMarkedForRemoval" id="TemplateIsMarkedForRemoval" type="checkbox" value="true" />
                                    <input name="TemplateIsMarkedForRemoval" type="hidden" value="false" />
                                </td>
                                <td class="text-center">
                                    <input name="TemplateIsMarkedForDeletion" id="TemplateIsMarkedForDeletion" type="checkbox" value="true" />
                                    <input name="TemplateIsMarkedForDeletion" type="hidden" value="false" />
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- Add / Remove Row Buttons -->
                <div class="row">
                    <div class="col-md-6 col-xs-12">
                        @*<button type="submit" name="submitAction" value="AddNew" id="btnAddNewInmate" class="btn btn-primary">
                                <span class="glyphicon glyphicon-plus"></span> Add New Inmate
                            </button>*@
                        <button type="submit" id="btnAddNewInmate" class="btn btn-primary">
                            <span class="glyphicon glyphicon-plus"></span> Add New Inmate
                        </button>
                    </div>
                    <div class="col-md-6 col-xs-12 text-right">
                        <button type="submit" name="submitAction" value="RemoveSelected" name="btnRemoveInmate" class="btn btn-danger">
                            <span class="glyphicon glyphicon-remove"></span> Remove Inmate
                        </button>
                        <span style="display:inline-block; width:20px;"></span>
                        <button type="submit" name="submitAction" value="DeleteSelected" id="btnDelete" class="btn btn-default">
                            <span class="glyphicon glyphicon-trash"></span> Delete
                        </button>
                    </div>
                </div>
                <br />
                <!-- Save / Cancel Buttons -->
                <div class="row text-center">
                    <button type="submit" name="submitAction" value="Save" class="btn btn-primary" id="btnSave">
                        <span class="glyphicon glyphicon-floppy-disk"></span> Save
                    </button>
                    <button type="submit" name="submitAction" value="Cancel" class="btn btn-default" id="btnCancel">
                        <span class="glyphicon glyphicon-remove-circle"></span> Cancel
                    </button>
                </div>
            </div>
        </div>


        @section PageCss
{
            <link href="~/Areas/Rh/Content/CountOffice/jquery-ui.css" rel="stylesheet" />
            <link href="~/Areas/Rh/Content/CountOffice/jquery-ui.theme.css" rel="stylesheet" />

            <style>
                /* Date picker styling */
                .datepicker-trigger {
                    background-color: #f5f5f5;
                    border-left: 1px solid #ccc;
                    padding: 5px 8px;
                }

                    .datepicker-trigger:hover {
                        background-color: #e8e8e8;
                    }

                    .datepicker-trigger .glyphicon-calendar {
                        color: #337ab7;
                        font-size: 12px;
                    }

                /* Ensure input group sizing is consistent */
                .input-group-sm .input-group-addon {
                    padding: 5px 8px;
                    font-size: 12px;
                    line-height: 1.5;
                    border-radius: 3px;
                }

                /* Custom datepicker styling to match the expected result */
                .ui-datepicker {
                    font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
                    font-size: 11px;
                    width: 200px;
                    border: 1px solid #999;
                    background: white;
                    box-shadow: 2px 2px 5px rgba(0,0,0,0.3);
                    padding: 0;
                }

                    .ui-datepicker .ui-datepicker-header {
                        background: #f0f0f0;
                        border: none;
                        border-bottom: 1px solid #ccc;
                        color: #333;
                        font-weight: normal;
                        padding: 4px 8px;
                        position: relative;
                        height: 20px;
                    }

                    .ui-datepicker .ui-datepicker-prev,
                    .ui-datepicker .ui-datepicker-next {
                        position: absolute;
                        top: 2px;
                        width: 16px;
                        height: 16px;
                        background: #f0f0f0;
                        border: 1px solid #999;
                        cursor: pointer;
                        text-align: center;
                        line-height: 14px;
                        font-size: 10px;
                        color: #333;
                    }

                    .ui-datepicker .ui-datepicker-prev {
                        left: 4px;
                    }

                    .ui-datepicker .ui-datepicker-next {
                        right: 4px;
                    }

                        .ui-datepicker .ui-datepicker-prev:hover,
                        .ui-datepicker .ui-datepicker-next:hover {
                            background: #e0e0e0;
                        }

                    .ui-datepicker .ui-datepicker-title {
                        line-height: 20px;
                        margin: 0 25px;
                        text-align: center;
                        font-weight: bold;
                        font-size: 11px;
                    }

                    /* Style the month and year dropdowns */
                    .ui-datepicker .ui-datepicker-month,
                    .ui-datepicker .ui-datepicker-year {
                        font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
                        font-size: 11px;
                        border: 1px solid #999;
                        background: white;
                        padding: 1px 2px;
                        margin: 0 2px;
                        color: #333;
                    }

                    .ui-datepicker .ui-datepicker-month {
                        width: 70px;
                    }

                    .ui-datepicker .ui-datepicker-year {
                        width: 50px;
                    }

                    .ui-datepicker table {
                        width: 100%;
                        font-size: 11px;
                        border-collapse: collapse;
                        margin: 0;
                        border-spacing: 0;
                    }

                    .ui-datepicker th {
                        padding: 4px 2px;
                        text-align: center;
                        font-weight: normal;
                        border: none;
                        background: #f8f8f8;
                        color: #666;
                        font-size: 10px;
                        border-bottom: 1px solid #ddd;
                    }

                    .ui-datepicker td {
                        border: none;
                        padding: 0;
                        text-align: center;
                    }

                        .ui-datepicker td span,
                        .ui-datepicker td a {
                            display: block;
                            padding: 2px;
                            text-align: center;
                            text-decoration: none;
                            border: 1px solid transparent;
                            font-size: 11px;
                            color: #333;
                            width: 20px;
                            height: 16px;
                            line-height: 16px;
                            margin: 1px;
                        }

                            .ui-datepicker td a:hover {
                                background: #316AC5;
                                color: white;
                                border: 1px solid #316AC5;
                            }

                    .ui-datepicker .ui-datepicker-today a {
                        background: #316AC5;
                        color: white;
                        font-weight: bold;
                        border: 1px solid #316AC5;
                    }

                    .ui-datepicker .ui-datepicker-current-day a {
                        background: #316AC5;
                        color: white;
                        font-weight: bold;
                        border: 1px solid #316AC5;
                    }

                    .ui-datepicker .ui-datepicker-other-month a {
                        color: #ccc;
                    }

                    /* Hide the default jQuery UI icons and use custom arrows */
                    .ui-datepicker .ui-icon {
                        display: none;
                    }

                    .ui-datepicker .ui-datepicker-prev:before {
                        content: "◀";
                        font-size: 8px;
                    }

                    .ui-datepicker .ui-datepicker-next:before {
                        content: "▶";
                        font-size: 8px;
                    }

                /* Comment textarea styling */
                .comment-textarea {
                    resize: vertical !important;
                    min-height: 34px !important;
                    max-height: 35px;
                    font-family: inherit;
                    font-size: 12px;
                    line-height: 1.4;
                    padding: 6px 12px;
                    border: 1px solid #ccc;
                    border-radius: 4px;
                    box-shadow: inset 0 1px 1px rgba(0,0,0,.075);
                    transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
                }

                    .comment-textarea:focus {
                        border-color: #66afe9;
                        outline: 0;
                        box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 8px rgba(102,175,233,.6);
                    }

                /* Ensure the table cell accommodates the resizable textarea */
                #inmateTable td:nth-child(8) {
                    vertical-align: top;
                    min-width: 250px;
                }
            </style>

        }


        @section PageScripts {


            <!-- Define MyScript object to prevent errors -->
            <script type="text/javascript">


            // Define MyScript object if it doesn't exist
            var MyScript = MyScript || {};

            // Add init method if it doesn't exist
            MyScript.init = MyScript.init || function(options) {
                console.log("MyScript.init called with options:", options);
                window.m_options = options; // Store options globally if needed
            };

            var MOSCI = MOSCI || {};
            MOSCI.MAX_ROWS = 20;

            // Function to validate duplicate offenders in the grid
            function validateDuplicateOffender(currentRowIndex, prefix, offenderId) {
                if (!prefix || !offenderId || offenderId.trim() === '') {
                    return { isValid: true, message: '' };
                }

                var combinedId = prefix + offenderId.trim();
                var duplicateFound = false;
                var duplicateRowIndex = -1;

                // Check all other rows for the same prefix + offender ID combination
                $('#inmateTable tbody tr').each(function (index) {
                    if (index !== currentRowIndex) {
                        var $row = $(this);
                        var rowPrefix = $row.find('select[id*="InmateIdPrefix"]').val() || '';
                        var rowOffenderId = $row.find('input[id*="OffenderId"]').val() || '';

                        if (rowPrefix && rowOffenderId.trim() !== '') {
                            var rowCombinedId = rowPrefix + rowOffenderId.trim();
                            if (rowCombinedId === combinedId) {
                                duplicateFound = true;
                                duplicateRowIndex = index + 1; // 1-based for user display
                                return false; // Break out of each loop
                            }
                        }
                    }
                });

                if (duplicateFound) {
                    alert(`Duplicate offender found! Offender ${combinedId} already exists in row ${duplicateRowIndex}.`);
                    return {
                        isValid: false,
                        message: `Duplicate offender found! Offender ${combinedId} already exists in row ${duplicateRowIndex}.`

                    };
                }

                return { isValid: true, message: '' };
            }

            // Function to show validation error message
            function showValidationError(message) {
                $('#ErrorMessage').text(message);
                $('#divErrorMessage').show();

                // Auto-hide after 10 seconds
                setTimeout(function () {
                    $('#divErrorMessage').hide();
                }, 1000);
            }

            // Function to hide validation error message
            function hideValidationError() {
                $('#divErrorMessage').hide();
            }


            // Auto-populate function for offender data - JavaScript-only implementation
            function autoPopulateOffender(inputElement, rowIndex, inmateIdPrefix) {
                var combinedOffenderId = inputElement.value.trim();
                console.log('=== Auto-populate triggered ===');
                console.log('Row Index:', rowIndex);
                console.log('Combined Offender ID:', combinedOffenderId);

                var $row = $('#inmateTable tbody tr').eq(rowIndex);

                if (!combinedOffenderId) {
                    // Clear fields if offender ID is empty
                    clearOffenderFields(rowIndex);
                    //hideValidationError();
                    return;
                }


                var prefix = inmateIdPrefix || "";

                var offenderId = combinedOffenderId;

                //// If no prefix passed extract from combined ID input
                //if (!prefix && combinedOffenderId.length > 0 && /^[A-Za-z]/.test(combinedOffenderId[0])) {
                //    prefix = combinedOffenderId[0].toUpperCase();
                //    offenderId = combinedOffenderId.substring(1);
                //}
                //else { offenderId = combinedOffenderId; }

                // Validate for duplicates before proceeding
                var validation = validateDuplicateOffender(rowIndex, prefix, offenderId);
                if (!validation.isValid) {
                    //showValidationError(validation.message);

                    // Clear the current row's offender ID to prevent duplicate
                    $row.find('input[id*="OffenderId"]').val('');
                    clearOffenderFields(rowIndex);
                    return;
                }

                // Hide any previous validation errors
                //hideValidationError();



                // Update the prefix dropdown and hidden OffenderId field
                if (prefix) {
                    $row.find('select[id*="InmateIdPrefix"]').val(prefix);
                }
                $row.find('input[id*="OffenderId"]').val(offenderId);


                // Make AJAX call to get offender data
                $.ajax({
                    url: '@Url.Action("GetOffenderData", "Mosci", new { area = "Transfers" })',
                    type: 'POST',
                    data: {
                        prefix: prefix,
                        offenderId: offenderId
                    },
                    success: function(result) {
                        console.log('Auto-populate response:', result);

                        if (result.success && result.offender) {
                            var offender = result.offender;

                            // Populate the fields using flexible selectors (ID or name attribute)
                            var $lastNameField = $row.find('input[id*="LastName"], input[name*="LastName"]');
                            var $firstNameField = $row.find('input[id*="FirstName"], input[name*="FirstName"]');
                            var $fromInsField = $row.find('input[id*="frominsText"], input[name*="frominsText"]');

                            $lastNameField.val(offender.lastName || '');
                            $firstNameField.val(offender.firstName || '');

                            // Set From Institution if available
                            if (offender.frominsText) {
                                $fromInsField.val(offender.frominsText || '');
                            }

                            console.log('Auto-populated:', offender.lastName, offender.firstName);
                            console.log('Fields found - LastName:', $lastNameField.length, 'FirstName:', $firstNameField.length, 'FromIns:', $fromInsField.length);
                        } else if (result.message = 'Inmate already schedule.')
                        {
                            //$('.alert-success').hide(); // hide success green messages

                            var message = result.message;
                            alert('You must complete the process of scheduling before you can proceed to unschedule an inmate');
                            //showValidationError(message);
                            //hideValidationError();

                            //$('.alert-success').show();
                        }
                        else {
                            // Clear fields if no match found using flexible selectors
                            $row.find('input[id*="LastName"], input[name*="LastName"]').val('');
                            $row.find('input[id*="FirstName"], input[name*="FirstName"]').val('');
                            $row.find('input[id*="frominsText"], input[name*="frominsText"]').val('');

                            alert('No matching offender found');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('Auto-populate error:', error);
                        // Clear fields on error using flexible selectors
                        $row.find('input[id*="LastName"], input[name*="LastName"]').val('');
                        $row.find('input[id*="FirstName"], input[name*="FirstName"]').val('');
                        $row.find('input[id*="frominsText"], input[name*="frominsText"]').val('');
                    }
                });
            }



            $(document).on('change', '.auto-populate-field', function () {
                var $this = $(this);
                var rowIndex = $this.data('row-index') || $this.closest('tr').index();
                var $row = $this.closest('tr');
                var inmateIdPrefix = $row.find('select[id*="InmateIdPrefix"], select[name*="InmateIdPrefix"]').val() || '';

                console.log('Change event - rowIndex:', rowIndex, 'inmateIdPrefix:', inmateIdPrefix);
                autoPopulateOffender(this, rowIndex, inmateIdPrefix);
            });

            $(document).on('keydown', '.auto-populate-field', function (event) {
                if (event.keyCode == 13 || event.keyCode == 9) { // Enter or Tab
                    var $this = $(this);
                    var rowIndex = $this.data('row-index') || $this.closest('tr').index();
                    var $row = $this.closest('tr');
                    var inmateIdPrefix = $row.find('select[id*="InmateIdPrefix"], select[name*="InmateIdPrefix"]').val() || '';

                    console.log('Keydown event - rowIndex:', rowIndex, 'inmateIdPrefix:', inmateIdPrefix);
                    autoPopulateOffender(this, rowIndex, inmateIdPrefix);
                    return false;
                }
            });

            // Function to clear offender fields
            function clearOffenderFields(rowIndex) {
                var $row = $('#inmateTable tbody tr').eq(rowIndex);
                $row.find('input[id*="LastName"], input[name*="LastName"]').val('');
                $row.find('input[id*="FirstName"], input[name*="FirstName"]').val('');
                $row.find('input[id*="frominsText"], input[name*="frominsText"]').val('');
                //$row.find('input[id*="FromInstitutionId"]').val('');
                $row.find('select[id*="InmateIdPrefix"], select[name*="InmateIdPrefix"]').val('');
                $row.find('input[id*="OffenderId"], input[name*="OffenderId"]').val('');
            }



            // Function to clear the table except for one empty row
            MOSCI.clearTable = function() {
                $('#inmateTable tbody tr').not(':first').remove();
                var $firstRow = $('#inmateTable tbody tr:first');
                $firstRow.find('input[type="text"]').val('');
                $firstRow.find('textarea').val('');
                $firstRow.find('select').prop('selectedIndex', 0);
                $firstRow.find('input[type="checkbox"]').prop('checked', false);
                $firstRow.find('input[type="hidden"]').val('');
            };
            // Function to update row indices and field names after adding/removing rows
            MOSCI.updateRowIndices = function () {
                $('#inmateTable tbody tr').each(function (index) {
                    var $row = $(this);

                    // Update all input field names and IDs to use the correct index
                    $row.find('input, select').each(function () {
                        var $field = $(this);
                        var name = $field.attr('name');
                        var id = $field.attr('id');

                        if (name && name.includes('Inmates[')) {
                            // Update the index in the name attribute
                            var newName = name.replace(/Inmates\[\d+\]/, 'Inmates[' + index + ']');
                            $field.attr('name', newName);
                        }

                        if (id && id.includes('Inmates_')) {
                            // Update the index in the id attribute
                            var newId = id.replace(/Inmates_\d+_/, 'Inmates_' + index + '_');
                            $field.attr('id', newId);
                        }
                    });

                    // Update data-row-index attribute for auto-populate fields
                    $row.find('.auto-populate-field').attr('data-row-index', index);
                });

                console.log('Updated row indices for', $('#inmateTable tbody tr').length, 'rows');
            };

            // Function to initialize datepickers
            MOSCI.initializeDatepickers = function () {
                // Initialize datepickers for all date inputs that don't already have it
                $('.datepicker-input').each(function () {
                    var $input = $(this);
                    if (!$input.hasClass('hasDatepicker')) {
                        console.log('Initializing datepicker for input:', $input[0]);
                        try {
                            $input.datepicker({
                                dateFormat: 'mm/dd/yy',
                                changeMonth: true,
                                changeYear: true,
                                showButtonPanel: false,
                                yearRange: '-10:+10',
                                showOtherMonths: true,
                                selectOtherMonths: false,
                                firstDay: 0, // Sunday
                                onSelect: function (dateText) {
                                    console.log('Date selected via datepicker:', dateText);
                                    $(this).trigger('change');
                                }
                            });

                            // Also add click handler to input itself
                            $input.on('focus click', function () {
                                console.log('Input focused/clicked, showing datepicker');
                                $(this).datepicker('show');
                            });

                            console.log('Datepicker initialized for input');
                        } catch (error) {
                            console.error('Error initializing datepicker:', error);
                        }
                    }
                });
            };

            // Handle calendar icon clicks with event delegation - simplified approach
            $(document).on('click', '.datepicker-trigger', function (e) {
                e.preventDefault();
                e.stopPropagation();

                var $trigger = $(this);
                var $input = $trigger.siblings('.datepicker-input');

                console.log('=== Calendar Icon Clicked ===');
                console.log('Trigger element:', $trigger[0]);
                console.log('Input found:', $input.length);
                console.log('Input element:', $input[0]);

                if ($input.length > 0) {
                    console.log('Input has datepicker class:', $input.hasClass('hasDatepicker'));

                    // Force initialize datepicker if not already done
                    if (!$input.hasClass('hasDatepicker')) {
                        console.log('Force initializing datepicker...');
                        try {
                            $input.datepicker({
                                dateFormat: 'mm/dd/yy',
                                changeMonth: true,
                                changeYear: true,
                                showButtonPanel: false,
                                yearRange: '-10:+10',
                                showOtherMonths: true,
                                selectOtherMonths: false,
                                firstDay: 0,
                                onSelect: function (dateText) {
                                    console.log('Date selected:', dateText);
                                    $(this).trigger('change');
                                }
                            });
                            console.log('Datepicker initialized successfully');
                        } catch (initError) {
                            console.error('Error initializing datepicker:', initError);
                            return;
                        }
                    }

                    // Try to show the datepicker
                    try {
                        console.log('Attempting to show datepicker...');
                        $input.datepicker('show');
                        console.log('Datepicker show command executed');
                    } catch (showError) {
                        console.error('Error showing datepicker:', showError);
                        // Alternative approach: focus the input which should trigger the datepicker
                        console.log('Trying alternative approach - focusing input');
                        $input.focus();

                        // If that doesn't work, try triggering a click event
                        setTimeout(function () {
                            $input.trigger('click');
                        }, 50);
                    }
                } else {
                    console.error('No input sibling found for calendar trigger');
                    console.log('Trigger parent:', $trigger.parent()[0]);
                    console.log('All inputs in parent:', $trigger.parent().find('input').length);
                }
            });



            $(function () {


                // Debug form submission
                $('#Mosci').on('submit', function (e) {
                    console.log('Form submission detected');
                    var formData = $(this).serialize();
                    console.log('Form data:', formData);

                    // Check if this is a search submission - look for the clicked button
                    var submitAction = $(document.activeElement).val() || $('button[type="submit"]:focus').val();
                    console.log('Submit action:', submitAction);

                    // Log search-specific values
                    var searchPrefix = $('#searchPrefixDropdown').val();
                    var searchOffenderId = $('#txtInmateNum').val();
                    console.log('Search values - Prefix:', searchPrefix, 'OffenderId:', searchOffenderId);

                    if (submitAction === 'Search') {
                        console.log('Search form submission detected');
                        console.log('Final search values being submitted - Prefix:', searchPrefix, 'OffenderId:', searchOffenderId);
                    } else if (submitAction === 'Save') {
                        console.log('=== SAVE FORM SUBMISSION DETECTED ===');

                        // Log all table rows and their data
                        var rowCount = $('#inmateTable tbody tr').length;
                        console.log('Total rows in table:', rowCount);

                        $('#inmateTable tbody tr').each(function (index) {
                            var $row = $(this);
                            var rowData = {
                                index: index,
                                //recno: $row.find('input[id*="Recno"]').val(),
                                prefix: $row.find('select[id*="InmateIdPrefix"]').val(),
                                offenderId: $row.find('input[id*="OffenderId"]').val(),
                                //combinedOffenderId: $row.find('input[id*="CombinedOffenderId"]').val(),
                                lastName: $row.find('input[id*="LastName"]').val(),
                                firstName: $row.find('input[id*="FirstName"]').val(),
                                fromInstitutionId: $row.find('input[id*="frominsText"]').val(),
                                //toInstitutionId: $row.find('select[id*="ToInstitutionId"]').val(),
                                schDate: $row.find('input[id*="SchDate"]').val(),
                                descrl: $row.find('textarea[id*="Descrl"]').val(),
                                isMarkedForRemoval: $row.find('input[id*="IsMarkedForRemoval"]:checked').length > 0,
                                isMarkedForDeletion: $row.find('input[id*="IsMarkedForDeletion"]:checked').length > 0
                            };
                            console.log('Row ' + index + ' data:', rowData);
                        });

                        // Log form field names to verify proper MVC binding
                        console.log('=== Form field names for MVC binding ===');
                        $('#inmateTable tbody tr').each(function (index) {
                            var $row = $(this);
                            $row.find('input, select').each(function () {
                                var $field = $(this);
                                var name = $field.attr('name');
                                var id = $field.attr('id');
                                var value = $field.val();
                                if (name && name.includes('Inmates[')) {
                                    console.log('Field:', name, 'ID:', id, 'Value:', value);
                                }
                            });
                        });
                    }
                });

            // Function to update field names for proper array indexing
            function updateFieldNames($row, index) {
                $row.find('input, select, textarea').each(function() {
                    var $field = $(this);
                    var name = $field.attr('name');
                    var id = $field.attr('id');

                    if (name && name.startsWith('Template')) {
                        // Replace Template prefix with proper array indexing
                        var fieldName = name.replace('Template', '');

                        // Special handling for frominsText field (it doesn't use array indexing)
                        if (fieldName === 'frominsText') {
                            $field.attr('name', 'frominsText');
                            // ID doesn't need array indexing for frominsText
                            if (id && id.startsWith('Template')) {
                                $field.attr('id', 'frominsText');
                            }
                        } else {
                            $field.attr('name', 'Inmates[' + index + '].' + fieldName);
                            // Update ID to match MVC naming convention
                            if (id && id.startsWith('Template')) {
                                var newId = 'Inmates_' + index + '_' + fieldName;
                                // Handle special case for hidden OffenderId field
                                if (id === 'TemplateOffenderId_Hidden') {
                                    newId = 'Inmates_' + index + '_OffenderId_Hidden';
                                }
                                $field.attr('id', newId);
                            }
                        }
                    }
                });
            }

            // Function to initialize all functionality for a new row
            function initializeNewRowFunctionality($newRow) {
                // 1. Initialize datepicker for date inputs
                $newRow.find('.datepicker-input').each(function () {
                    var $input = $(this);
                    if (!$input.hasClass('hasDatepicker')) {
                        $input.datepicker({
                            dateFormat: 'mm/dd/yy',
                            changeMonth: true,
                            changeYear: true,
                            showButtonPanel: false,
                            yearRange: '-10:+10',
                            showOtherMonths: true,
                            selectOtherMonths: false,
                            firstDay: 0 // Sunday
                        });
                    }
                });

                // 2. Initialize numeric validation for offender ID fields
                $newRow.find('.onlyNumeric').off('keypress.numeric').on('keypress.numeric', function(e) {
                    // Allow only numbers (0-9)
                    if (e.which < 48 || e.which > 57) {
                        e.preventDefault();
                    }
                });

                // 3. Set up auto-populate functionality
                // The auto-populate events are already bound using $(document).on()
                // so they will work automatically for new elements with .auto-populate-field class

                // 4. Initialize datepicker trigger functionality
                $newRow.find('.datepicker-trigger').off('click.datepicker').on('click.datepicker', function() {
                    $(this).siblings('.datepicker-input').datepicker('show');
                });

                // 5. Update row index data attribute for auto-populate
                var rowIndex = $newRow.index();
                $newRow.find('.auto-populate-field').attr('data-row-index', rowIndex);

                console.log('Initialized functionality for new row at index:', rowIndex);
            }

            // Clone first row and clear inputs when adding a new inmate
            $('#btnAddNewInmate').on('click', function (e) {
                e.preventDefault();

                // Check if we've reached the maximum number of rows
                var currentRowCount = $('#inmateTable tbody tr').length;
                if (currentRowCount >= MOSCI.MAX_ROWS) {
                    alert('Maximum number of rows (' + MOSCI.MAX_ROWS + ') reached. Cannot add more rows.');
                    return null;
                }

                var $newRow;

                // Check if there are existing rows to clone from
                if (currentRowCount > 0) {
                    // Clone the template row or first row
                    var $templateRow = $('#inmate-row-template');
                    if ($templateRow.length === 0) {
                        $templateRow = $('#inmateTable tbody tr:first');
                    }
                    $newRow = $templateRow.clone();

                    // Generate a unique ID for the new row
                    var rowId = 'inmate-row-' + new Date().getTime();
                    $newRow.attr('id', rowId);

                    // Clear all input values
                    $newRow.find('input[type="text"]').val('');
                    $newRow.find('textarea').val('');
                    $newRow.find('select').prop('selectedIndex', 0);
                    $newRow.find('input[type="checkbox"]').prop('checked', false);

                    // Update field names for proper array indexing
                    $newRow.find('input, select, textarea').each(function() {
                        var $field = $(this);
                        var name = $field.attr('name');
                        if (name && name.includes('Inmates[')) {
                            // Update the index in the field name
                            var newName = name.replace(/Inmates\[\d+\]/, 'Inmates[' + currentRowCount + ']');
                            $field.attr('name', newName);
                        }
                    });
                } else {
                    // Use the hidden template when no rows exist
                    $newRow = $('#empty-row-template').clone();

                    // Generate a unique ID for the new row
                    var rowId = 'inmate-row-' + new Date().getTime();
                    $newRow.attr('id', rowId);

                    // Update the field names to use proper array indexing
                    updateFieldNames($newRow, currentRowCount);
                }

                // Append the new row to the table
                $('#inmateTable tbody').append($newRow);

                // Initialize all functionality for the new row
                initializeNewRowFunctionality($newRow);

                //update button state after adding new rows
                updateButtonState();

                // Update row indices for all rows
                MOSCI.updateRowIndices();

                // Scroll to the new row
                $('html, body').animate({
                    scrollTop: $newRow.offset().top - 100
                }, 500);
            });

            // Wait a bit for jQuery UI to be fully loaded
            setTimeout(function () {
                console.log('=== Initializing Datepickers ===');
                console.log('Found datepicker inputs:', $('.datepicker-input').length);

                // Initialize datepickers for existing rows
                MOSCI.initializeDatepickers();

                // Verify initialization
                setTimeout(function () {
                    console.log('Datepicker inputs with hasDatepicker class:', $('.datepicker-input.hasDatepicker').length);

                    // Test direct click on first input
                    var $firstInput = $('.datepicker-input').first();
                    if ($firstInput.length > 0) {
                        console.log('First input element:', $firstInput[0]);
                        console.log('First input has datepicker:', $firstInput.hasClass('hasDatepicker'));
                    }
                }, 200);
            }, 100);

            // Handle remove inmate button - using submitAction value instead of name
            $('button[value="RemoveSelected"]').on('click', function(e) {
                e.preventDefault();

                var hasChecked = false;
                var checkedCount = 0;
                var totalRows = $('#inmateTable tbody tr').length;

                // Count checked rows first
                $('#inmateTable tbody tr').each(function() {
                    var $row = $(this);
                    if ($row.find('input[id*="IsMarkedForRemoval"]').is(':checked')) {
                        checkedCount++;
                    }
                });

                if (checkedCount === 0) {
                    alert('Please select at least one inmate to remove.');
                    return;
                }

                // Check if removing would leave at least one row
                if (totalRows - checkedCount < 1) {
                    alert('Cannot remove all rows. At least one row must remain in the table.');
                    return;
                }

                //confirmation dialog
                if (!confirm("Are you sure you want to remove selected inmates")) { return; }

                // Remove the checked rows
                $('#inmateTable tbody tr').each(function() {
                    var $row = $(this);
                    if ($row.find('input[id*="IsMarkedForRemoval"]').is(':checked')) {
                        $row.remove();
                        hasChecked = true;
                    }
                });

                if (hasChecked) {
                    alert('Selected inmate(s) removed successfully.');
                }

                updateButtonState();
            });



            // Handle delete button - using AJAX calls for individual record deletion
            $('button[value="DeleteSelected"]').on('click', function(e) {
                e.preventDefault();

                var checkedRows = [];
                var totalRows = $('#inmateTable tbody tr').length;

                // Collect all checked rows with their data
                $('#inmateTable tbody tr').each(function() {
                    var $row = $(this);
                    if ($row.find('input[id*="IsMarkedForDeletion"]').is(':checked')) {
                        var prefix = $row.find('select[id*="InmateIdPrefix"]').val();
                        var offenderId = $row.find('input[id*="OffenderId"]').val();
                        var schduleDate = $row.find('input[id*="SchDate"]').val();

                        // Only include rows with valid data
                        if (prefix && offenderId && offenderId.trim() !== '' && schduleDate !== '') {
                            checkedRows.push({
                                row: $row,
                                prefix: prefix,
                                offenderId: offenderId.trim(),
                                schduleDate: schduleDate
                            });
                        }
                    }
                });

                // Validation: Check if any rows are selected
                if (checkedRows.length === 0) {
                    alert('Please select at least one inmate with valid data to delete.');
                    return;
                }
                // Validation: offender exist in trans 3 screen
                //if (schduleDate !== '') {
                //    alert('Please select at least one inmate with valid data to delete.');
                //    return;
                //}

                // Show confirmation popup with exact text as requested
                if (!confirm('Are you sure you want to delete an inmate from MOSCI?')) {
                    // User clicked "No" - do nothing
                    return;
                }

                // User clicked "Yes" - proceed with deletion
                var deletionPromises = [];
                var successCount = 0;
                var errorCount = 0;
                var errorMessages = [];

               // Process each checked row
                checkedRows.forEach(function(rowData) {
                    // Create the data structure expected by MosciPageViewModel
                    var viewModelData = {
                        'Inmates[0].InmateIdPrefix': rowData.prefix,
                        'Inmates[0].OffenderId': rowData.offenderId,
                        'Inmates[0].IsMarkedForDeletion': true,
                        'Inmates[0].SchDate': rowData.schduleDate
                    };

                    var promise = $.ajax({
                        url: '@Url.Action("DeleteMosciRecord", "Mosci", new { area = "Transfers" })',
                        type: 'POST',
                        data: viewModelData
                    }).done(function(response) {
                        if (response.success) {
                            // Clear the row (make it blank but keep the structure)
                            rowData.row.find('input[type="text"]').val('');
                            rowData.row.find('textarea').val('');
                            rowData.row.find('select').prop('selectedIndex', 0);
                            rowData.row.find('input[type="checkbox"]').prop('checked', false);
                            rowData.row.find('input[type="hidden"]').val('');
                            successCount++;
                        } else {
                            errorCount++;
                            errorMessages.push(response.message || 'Unknown error occurred');
                        }
                    }).fail(function(xhr, status, error) {
                        errorCount++;
                        errorMessages.push('Network error: ' + error);
                    });

                    deletionPromises.push(promise);
                });

                // Wait for all deletion requests to complete
                $.when.apply($, deletionPromises).always(function() {
                    // Show results to user
                    if (successCount > 0 && errorCount === 0) {
                        // All deletions successful
                        console.log('All selected inmates deleted successfully from MOSCI.');
                    } else if (successCount > 0 && errorCount > 0) {
                        // Some successful, some failed
                        alert('Some deletions completed successfully, but ' + errorCount + ' failed:\n' + errorMessages.join('\n'));
                    } else if (errorCount > 0) {
                        // All failed
                        alert('Failed to delete inmates:\n' + errorMessages.join('\n'));
                    }

                    updateButtonState();
                });
            });


            // Handle cancel button
            $('#btnCancel').on('click', function() {
                if (confirm('Are you sure you want to cancel? Any unsaved changes will be lost.')) {
                    window.location.href = '@Url.Action("Index", "Home", new { area = "" })';
                }
            });


            //var updateButtonState = function () {
            //    var rowCount = $('#inmateTable tbody tr').length;
            //    $('#btnAddNewInmate').prop('disabled', rowCount >= MOSCI.MAX_ROWS);
            //    console.log('Row count: ' + rowCount + ', Add button ' + (rowCount >= MOSCI.MAX_ROWS ? 'disabled' : 'enabled'));
            //};

                var updateButtonState = function () {
                    var rowCount = $('#inmateTable tbody tr').length;
                    var isMaxRowsReached = rowCount >= MOSCI.MAX_ROWS;

                    // Check if any schedule date is not minimum value (not empty/default)
                    var hasNonMinScheduleDate = false;
                    $('#inmateTable tbody tr').each(function () {
                        var $row = $(this);
                        var schDateValue = $row.find('input[id*="SchDate"]').val();

                        // Check if the date field has a value and is not empty
                        if (schDateValue && schDateValue.trim() !== '') {
                            // Check for specific minimum date patterns that indicate DateTime.MinValue
                            var isMinValue = schDateValue === '01/01/0001' ||
                                schDateValue === '1/1/0001' ||
                                schDateValue === '01/01/1' ||
                                schDateValue === '1/1/1';

                            // If it's not a minimum value pattern, then it's a real date
                            if (!isMinValue) {
                                var parsedDate = new Date(schDateValue);
                                // If it's a valid date and not a minimum value, disable the button
                                if (!isNaN(parsedDate.getTime())) {
                                    hasNonMinScheduleDate = true;
                                    return false; // break out of each loop
                                }
                            }
                        }
                    });

                    // Disable button if max rows reached OR if any schedule date is not minimum value
                    var shouldDisable = isMaxRowsReached || hasNonMinScheduleDate;
                    $('#btnAddNewInmate').prop('disabled', shouldDisable);

                    // Update button title/tooltip to explain why it's disabled
                    var buttonTitle = '';
                    if (isMaxRowsReached) {
                        buttonTitle = 'Maximum number of rows reached. Cannot add more inmates.';
                    } else if (hasNonMinScheduleDate) {
                        buttonTitle = 'Cannot add new inmates when schedule date is set. Please clear schedule dates first.';
                    } else {
                        buttonTitle = 'Add a new inmate row';
                    }
                    $('#btnAddNewInmate').attr('title', buttonTitle);

                    var reason = '';
                    if (isMaxRowsReached) {
                        reason = 'maximum rows reached';
                    } else if (hasNonMinScheduleDate) {
                        reason = 'schedule date is not minimum value';
                    } else {
                        reason = 'enabled';
                    }

                    console.log('Row count: ' + rowCount + ', Add button ' + (shouldDisable ? 'disabled' : 'enabled') + ' (' + reason + ')');
                };



            updateButtonState();

            // Initialize numeric validation for existing rows
            $('.onlyNumeric').on('keypress', function(e) {
                // Allow only numbers (0-9)
                if (e.which < 48 || e.which > 57) {
                    e.preventDefault();
                }
            });

            // Add event handler for prefix dropdown changes to update combined offender ID
            $(document).on('change', 'select[id*="InmateIdPrefix"], select[name*="InmateIdPrefix"]', function() {
                var $row = $(this).closest('tr');
                var prefix = $(this).val();
                var offenderId = $row.find('input[id*="OffenderId"], input[name*="OffenderId"]').val();

                var rowIndex = $row.index();

                // Validate for duplicates when prefix changes
                if (prefix && offenderId && offenderId.trim() !== '') {
                    var validation = validateDuplicateOffender(rowIndex, prefix, offenderId.trim());
                    if (!validation.isValid) {
                        showValidationError(validation.message);
                        // Reset the prefix to empty to prevent duplicate
                        $(this).val('');
                        return;
                    } else {
                        hideValidationError();
                    }
                }

                var combinedId = prefix && offenderId ? prefix + offenderId : '';
                $row.find('input[id*="CombinedOffenderId"]').val(combinedId);
            });

                // Add event handler for To Institution dropdown changes to validate against From Institution
                $(document).on('change', 'select[id*="SchdInst"]', function () {
                    var $row = $(this).closest('tr');
                    var fromInstitution = $row.find('input[name*="frominsText"]').val();
                    var toInstitutionDropdown = $(this);
                    var toInstitutionText = toInstitutionDropdown.find('option:selected').text();
                    // Check if From and To institutions are the same
                    if (fromInstitution && toInstitutionText && fromInstitution.trim() === toInstitutionText.trim()) {
                        alert('From and To institutions cannot be the same. Please select a different destination institution.');
                        // Reset the dropdown to empty/default selection
                        toInstitutionDropdown.val('');
                        return false;
                    }
                });

            @*MyScript.init({
                MOSCI: '@Url.Action("Mosci", "Mosci", new { area = "Transfers" })',
                    GetEmployeeInfoByOaksId: '@Url.Action("GetEmployeeInfoByOaksId", "Mosci", new { area = "Transfers" })'
                });*@




            // Enable Enter key to trigger search
            $("#txtInmateNum").keyup(function (event) {
                if (event.keyCode == 13) {
                    $("#btnFindOffender").click();
                }
            });
            $('.scheduled-date input').datepicker('destroy');
            // Attach datepicker to all scheduled date inputs
            $('.scheduled-date input').datepicker({
                format: 'mm/dd/yyyy',
                autoclose: true,
                todayHighlight: true
            });


            $('.scheduled-date .date-icon').on('click', function (e) {
                e.preventDefault();
                var $input = $(this).closest('.scheduled-date').find('input');
                $input.datepicker('show');
            });



                // Handle save button - ensure all data is properly prepared before submission
                $('#btnSave').on('click', function (e) {
                    console.log('=== SAVE BUTTON CLICKED ===');

                    // Update all row indices to ensure proper MVC binding
                    //MOSCI.updateRowIndices();

                    // Validate that we have at least one row with meaningful data
                    var hasValidData = false;
                    $('#inmateTable tbody tr').each(function () {
                        var $row = $(this);

                        var offenderId = $row.find('input[id*="OffenderId"]').val();
                        //var prefix = $row.find('select[id*="InmateIdPrefix"]').val(),
                        //var lastName = $row.find('input[id*="LastName"]').val(),
                        //var firstName = $row.find('input[id*="FirstName"]').val(),
                        //var fromInstitutionId = $row.find('input[id*="frominsText"]').val(),
                        //var toInstitutionId = $row.find('select[id*="ToInstitutionId"]').val(),
                        //var schDat = $row.find('input[id*="SchDate"]').val(),

                        //if (offenderId && offenderId.trim() !== '' && lastName && lastName.trim() !== '' && firstName && firstName.trim() !== '' && schDat && schDat.trim() !== '') {
                        if (offenderId && offenderId.trim() !== '') {
                            hasValidData = true;
                            return false; // break out of each loop
                        }
                    });

                    if (!hasValidData) {
                        alert('Please add at least one inmate with an Offender ID and destination institution before saving.');
                        e.preventDefault();
                        return false;
                    }

                    // Validate that From and To institutions are not the same for any row
                    var hasFromToConflict = false;
                    var conflictRowNumber = 0;
                    $('#inmateTable tbody tr').each(function (index) {
                        var $row = $(this);
                        var offenderId = $row.find('input[id*="OffenderId"]').val();

                        // Only validate rows that have data
                        if (offenderId && offenderId.trim() !== '') {
                            var fromInstitution = $row.find('input[name*="frominsText"]').val();
                            var toInstitutionDropdown = $row.find('select[id*="SchdInst"]');
                            var toInstitutionText = toInstitutionDropdown.find('option:selected').text();

                            if (fromInstitution && toInstitutionText && fromInstitution.trim() === toInstitutionText.trim()) {
                                hasFromToConflict = true;
                                conflictRowNumber = index + 1;
                                return false; // break out of each loop
                            }
                        }
                    });

                    if (hasFromToConflict) {
                        alert('Row ' + conflictRowNumber + ': From and To institutions cannot be the same. Please select a different destination institution.');
                        e.preventDefault();
                        return false;
                    }

                    console.log('Save validation passed, proceeding with form submission');
                    return true;
                });

        });
            </script>


            <script type="text/javascript" src="~/Areas/Rh/Scripts/Common.js"></script>
        }
    }